.itemList {
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
}

.itemList h1 {
  text-align: center;
  margin-bottom: 20px;
}

.itemList ul {
  list-style-type: none;
  padding: 0;
}

.itemList li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  margin-bottom: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.addItem {
  display: flex;
  margin: 20px 0;
  gap: 10px;
}

.addItem input {
  flex-grow: 1;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.filter {
  margin-top: 20px;
}

.itemList button {
  background-color: #333;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
}

.itemList button:hover {
  background-color: #555;
}