'use client'

import { useState } from 'react';
import styles from './ItemList.module.css';

export default function ItemList() {
  // Initialize state for items list
  const [items, setItems] = useState(['Apple', 'Banana', 'Cherry', 'Avocado']);
  // State for input field
  const [newItem, setNewItem] = useState('');
  // State for filter checkbox
  const [showOnlyA, setShowOnlyA] = useState(false);

  // Add new item to the list
  const handleAddItem = () => {
    if (newItem.trim()) {
      setItems([...items, newItem]);
      setNewItem(''); // Clear input field
    }
  };

  // Delete item from the list
  const handleDeleteItem = (index) => {
    const updatedItems = [...items];
    updatedItems.splice(index, 1);
    setItems(updatedItems);
  };

  // Filter items based on checkbox state
  const filteredItems = showOnlyA 
    ? items.filter(item => item.toLowerCase().startsWith('a'))
    : items;

  return (
    <div className={styles.itemList}>
      <h1>Item List</h1>
      
      {/* List display */}
      <ul>
        {filteredItems.map((item, index) => (
          <li key={index}>
            {item}
            <button onClick={() => handleDeleteItem(index)}>Delete</button>
          </li>
        ))}
      </ul>
      
      {/* Add item form */}
      <div className={styles.addItem}>
        <input
          type="text"
          value={newItem}
          onChange={(e) => setNewItem(e.target.value)}
          placeholder="Enter new item"
          onKeyPress={(e) => e.key === 'Enter' && handleAddItem()}
        />
        <button onClick={handleAddItem}>Add Item</button>
      </div>
      
      {/* Filter checkbox */}
      <div className={styles.filter}>
        <label>
          <input
            type="checkbox"
            checked={showOnlyA}
            onChange={() => setShowOnlyA(!showOnlyA)}
          />
          Show Only Items Starting with 'A'
        </label>
      </div>
    </div>
  );
}